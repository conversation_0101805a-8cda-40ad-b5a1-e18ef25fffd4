import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  require_cjs
} from "./chunk-EXQLYBKH.js";
import {
  require_operators
} from "./chunk-HGVHWTGE.js";
import {
  __toESM
} from "./chunk-GBTWTWDP.js";

// node_modules/@angular/cdk/fesm2022/scrolling.mjs
var import_rxjs = __toESM(require_cjs(), 1);
var import_operators = __toESM(require_operators(), 1);
//# sourceMappingURL=chunk-YALVPMF7.js.map
