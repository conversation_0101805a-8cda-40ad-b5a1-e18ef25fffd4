{"version": 3, "sources": ["../../../../../../node_modules/@angular/animations/fesm2022/animations.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.5\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ANIMATION_MODULE_TYPE, ViewEncapsulation, ɵRuntimeError as _RuntimeError, Injectable, Inject } from '@angular/core';\nimport { s as sequence } from './private_export-DBN_2NMM.mjs';\nexport { d as AUTO_STYLE, A as AnimationMetadataType, N as NoopAnimationPlayer, a as animate, b as animateChild, c as animation, g as group, k as keyframes, q as query, e as stagger, f as state, h as style, t as transition, i as trigger, u as useAnimation, j as ɵAnimationGroupPlayer, ɵ as ɵPRE_STYLE } from './private_export-DBN_2NMM.mjs';\n\n/**\n * An injectable service that produces an animation sequence programmatically within an\n * Angular component or directive.\n * Provided by the `BrowserAnimationsModule` or `NoopAnimationsModule`.\n *\n * @usageNotes\n *\n * To use this service, add it to your component or directive as a dependency.\n * The service is instantiated along with your component.\n *\n * Apps do not typically need to create their own animation players, but if you\n * do need to, follow these steps:\n *\n * 1. Use the <code>[AnimationBuilder.build](api/animations/AnimationBuilder#build)()</code> method\n * to create a programmatic animation. The method returns an `AnimationFactory` instance.\n *\n * 2. Use the factory object to create an `AnimationPlayer` and attach it to a DOM element.\n *\n * 3. Use the player object to control the animation programmatically.\n *\n * For example:\n *\n * ```ts\n * // import the service from BrowserAnimationsModule\n * import {AnimationBuilder} from '@angular/animations';\n * // require the service as a dependency\n * class MyCmp {\n *   constructor(private _builder: AnimationBuilder) {}\n *\n *   makeAnimation(element: any) {\n *     // first define a reusable animation\n *     const myAnimation = this._builder.build([\n *       style({ width: 0 }),\n *       animate(1000, style({ width: '100px' }))\n *     ]);\n *\n *     // use the returned factory object to create a player\n *     const player = myAnimation.create(element);\n *\n *     player.play();\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nclass AnimationBuilder {\n  static ɵfac = function AnimationBuilder_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AnimationBuilder)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AnimationBuilder,\n    factory: () => (() => inject(BrowserAnimationBuilder))(),\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AnimationBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => inject(BrowserAnimationBuilder)\n    }]\n  }], null, null);\n})();\n/**\n * A factory object returned from the\n * <code>[AnimationBuilder.build](api/animations/AnimationBuilder#build)()</code>\n * method.\n *\n * @publicApi\n */\nclass AnimationFactory {}\nclass BrowserAnimationBuilder extends AnimationBuilder {\n  animationModuleType = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  _nextAnimationId = 0;\n  _renderer;\n  constructor(rootRenderer, doc) {\n    super();\n    const typeData = {\n      id: '0',\n      encapsulation: ViewEncapsulation.None,\n      styles: [],\n      data: {\n        animation: []\n      }\n    };\n    this._renderer = rootRenderer.createRenderer(doc.body, typeData);\n    if (this.animationModuleType === null && !isAnimationRenderer(this._renderer)) {\n      // We only support AnimationRenderer & DynamicDelegationRenderer for this AnimationBuilder\n      throw new _RuntimeError(3600 /* RuntimeErrorCode.BROWSER_ANIMATION_BUILDER_INJECTED_WITHOUT_ANIMATIONS */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'Angular detected that the `AnimationBuilder` was injected, but animation support was not enabled. ' + 'Please make sure that you enable animations in your application by calling `provideAnimations()` or `provideAnimationsAsync()` function.');\n    }\n  }\n  build(animation) {\n    const id = this._nextAnimationId;\n    this._nextAnimationId++;\n    const entry = Array.isArray(animation) ? sequence(animation) : animation;\n    issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n    return new BrowserAnimationFactory(id, this._renderer);\n  }\n  static ɵfac = function BrowserAnimationBuilder_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BrowserAnimationBuilder)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BrowserAnimationBuilder,\n    factory: BrowserAnimationBuilder.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserAnimationBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.RendererFactory2\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\nclass BrowserAnimationFactory extends AnimationFactory {\n  _id;\n  _renderer;\n  constructor(_id, _renderer) {\n    super();\n    this._id = _id;\n    this._renderer = _renderer;\n  }\n  create(element, options) {\n    return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n  }\n}\nclass RendererAnimationPlayer {\n  id;\n  element;\n  _renderer;\n  parentPlayer = null;\n  _started = false;\n  constructor(id, element, options, _renderer) {\n    this.id = id;\n    this.element = element;\n    this._renderer = _renderer;\n    this._command('create', options);\n  }\n  _listen(eventName, callback) {\n    return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n  }\n  _command(command, ...args) {\n    issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n  }\n  onDone(fn) {\n    this._listen('done', fn);\n  }\n  onStart(fn) {\n    this._listen('start', fn);\n  }\n  onDestroy(fn) {\n    this._listen('destroy', fn);\n  }\n  init() {\n    this._command('init');\n  }\n  hasStarted() {\n    return this._started;\n  }\n  play() {\n    this._command('play');\n    this._started = true;\n  }\n  pause() {\n    this._command('pause');\n  }\n  restart() {\n    this._command('restart');\n  }\n  finish() {\n    this._command('finish');\n  }\n  destroy() {\n    this._command('destroy');\n  }\n  reset() {\n    this._command('reset');\n    this._started = false;\n  }\n  setPosition(p) {\n    this._command('setPosition', p);\n  }\n  getPosition() {\n    return unwrapAnimationRenderer(this._renderer)?.engine?.players[this.id]?.getPosition() ?? 0;\n  }\n  totalTime = 0;\n}\nfunction issueAnimationCommand(renderer, element, id, command, args) {\n  renderer.setProperty(element, `@@${id}:${command}`, args);\n}\n/**\n * The following 2 methods cannot reference their correct types (AnimationRenderer &\n * DynamicDelegationRenderer) since this would introduce a import cycle.\n */\nfunction unwrapAnimationRenderer(renderer) {\n  const type = renderer.ɵtype;\n  if (type === 0 /* AnimationRendererType.Regular */) {\n    return renderer;\n  } else if (type === 1 /* AnimationRendererType.Delegated */) {\n    return renderer.animationRenderer;\n  }\n  return null;\n}\nfunction isAnimationRenderer(renderer) {\n  const type = renderer.ɵtype;\n  return type === 0 /* AnimationRendererType.Regular */ || type === 1 /* AnimationRendererType.Delegated */;\n}\nexport { AnimationBuilder, AnimationFactory, sequence, BrowserAnimationBuilder as ɵBrowserAnimationBuilder };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA0DA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,OAAO,MAAM,OAAO,uBAAuB,GAAG;AAAA,IACvD,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,YAAY,MAAM,OAAO,uBAAuB;AAAA,IAClD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,mBAAN,MAAuB;AAAC;AACxB,IAAM,0BAAN,MAAM,iCAAgC,iBAAiB;AAAA,EACrD,sBAAsB,OAAO,uBAAuB;AAAA,IAClD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,mBAAmB;AAAA,EACnB;AAAA,EACA,YAAY,cAAc,KAAK;AAC7B,UAAM;AACN,UAAM,WAAW;AAAA,MACf,IAAI;AAAA,MACJ,eAAe,kBAAkB;AAAA,MACjC,QAAQ,CAAC;AAAA,MACT,MAAM;AAAA,QACJ,WAAW,CAAC;AAAA,MACd;AAAA,IACF;AACA,SAAK,YAAY,aAAa,eAAe,IAAI,MAAM,QAAQ;AAC/D,QAAI,KAAK,wBAAwB,QAAQ,CAAC,oBAAoB,KAAK,SAAS,GAAG;AAE7E,YAAM,IAAI,aAAc,OAAoF,OAAO,cAAc,eAAe,cAAc,4OAAiP;AAAA,IACjZ;AAAA,EACF;AAAA,EACA,MAAMA,YAAW;AACf,UAAM,KAAK,KAAK;AAChB,SAAK;AACL,UAAM,QAAQ,MAAM,QAAQA,UAAS,IAAI,SAASA,UAAS,IAAIA;AAC/D,0BAAsB,KAAK,WAAW,MAAM,IAAI,YAAY,CAAC,KAAK,CAAC;AACnE,WAAO,IAAI,wBAAwB,IAAI,KAAK,SAAS;AAAA,EACvD;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAA4B,SAAY,gBAAgB,GAAM,SAAS,QAAQ,CAAC;AAAA,EACnH;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,yBAAwB;AAAA,IACjC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,0BAAN,cAAsC,iBAAiB;AAAA,EACrD;AAAA,EACA;AAAA,EACA,YAAY,KAAK,WAAW;AAC1B,UAAM;AACN,SAAK,MAAM;AACX,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO,SAAS,SAAS;AACvB,WAAO,IAAI,wBAAwB,KAAK,KAAK,SAAS,WAAW,CAAC,GAAG,KAAK,SAAS;AAAA,EACrF;AACF;AACA,IAAM,0BAAN,MAA8B;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf,WAAW;AAAA,EACX,YAAY,IAAI,SAAS,SAAS,WAAW;AAC3C,SAAK,KAAK;AACV,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,SAAS,UAAU,OAAO;AAAA,EACjC;AAAA,EACA,QAAQ,WAAW,UAAU;AAC3B,WAAO,KAAK,UAAU,OAAO,KAAK,SAAS,KAAK,KAAK,EAAE,IAAI,SAAS,IAAI,QAAQ;AAAA,EAClF;AAAA,EACA,SAAS,YAAY,MAAM;AACzB,0BAAsB,KAAK,WAAW,KAAK,SAAS,KAAK,IAAI,SAAS,IAAI;AAAA,EAC5E;AAAA,EACA,OAAO,IAAI;AACT,SAAK,QAAQ,QAAQ,EAAE;AAAA,EACzB;AAAA,EACA,QAAQ,IAAI;AACV,SAAK,QAAQ,SAAS,EAAE;AAAA,EAC1B;AAAA,EACA,UAAU,IAAI;AACZ,SAAK,QAAQ,WAAW,EAAE;AAAA,EAC5B;AAAA,EACA,OAAO;AACL,SAAK,SAAS,MAAM;AAAA,EACtB;AAAA,EACA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO;AACL,SAAK,SAAS,MAAM;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,QAAQ;AACN,SAAK,SAAS,OAAO;AAAA,EACvB;AAAA,EACA,UAAU;AACR,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,SAAS;AACP,SAAK,SAAS,QAAQ;AAAA,EACxB;AAAA,EACA,UAAU;AACR,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,QAAQ;AACN,SAAK,SAAS,OAAO;AACrB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,YAAY,GAAG;AACb,SAAK,SAAS,eAAe,CAAC;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,WAAO,wBAAwB,KAAK,SAAS,GAAG,QAAQ,QAAQ,KAAK,EAAE,GAAG,YAAY,KAAK;AAAA,EAC7F;AAAA,EACA,YAAY;AACd;AACA,SAAS,sBAAsB,UAAU,SAAS,IAAI,SAAS,MAAM;AACnE,WAAS,YAAY,SAAS,KAAK,EAAE,IAAI,OAAO,IAAI,IAAI;AAC1D;AAKA,SAAS,wBAAwB,UAAU;AACzC,QAAM,OAAO,SAAS;AACtB,MAAI,SAAS,GAAuC;AAClD,WAAO;AAAA,EACT,WAAW,SAAS,GAAyC;AAC3D,WAAO,SAAS;AAAA,EAClB;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,UAAU;AACrC,QAAM,OAAO,SAAS;AACtB,SAAO,SAAS,KAAyC,SAAS;AACpE;", "names": ["animation"]}