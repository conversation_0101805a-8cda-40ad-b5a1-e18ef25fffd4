import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  AngularAppEngine,
  InlineCriticalCssProcessor,
  PrerenderFallback,
  RenderMode,
  createRequestHandler,
  destroyAngularServerApp,
  extractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig,
  provideServerRoutesConfig,
  provideServerRouting,
  setAngularAppEngineManifest,
  setAngularAppManifest,
  withAppShell
} from "./chunk-SDPVE4PP.js";
import "./chunk-IZD4NC7L.js";
import "./chunk-YXHH5FYS.js";
import "./chunk-LZKD5DHC.js";
import "./chunk-3OQGC6KC.js";
import "./chunk-KVMR2OJD.js";
import "./chunk-BY4T57VN.js";
import "./chunk-QOJB7MXJ.js";
import "./chunk-HGNYHDJR.js";
import "./chunk-EXQLYBKH.js";
import "./chunk-HGVHWTGE.js";
import "./chunk-IUOK4BIQ.js";
import "./chunk-GBTWTWDP.js";
export {
  AngularAppEngine,
  PrerenderFallback,
  RenderMode,
  createRequestHandler,
  provideServerRoutesConfig,
  provideServerRouting,
  withAppShell,
  InlineCriticalCssProcessor as ɵInlineCriticalCssProcessor,
  destroyAngularServerApp as ɵdestroyAngularServerApp,
  extractRoutesAndCreateRouteTree as ɵextractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp as ɵgetOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig as ɵgetRoutesFromAngularRouterConfig,
  setAngularAppEngineManifest as ɵsetAngularAppEngineManifest,
  setAngularAppManifest as ɵsetAngularAppManifest
};
