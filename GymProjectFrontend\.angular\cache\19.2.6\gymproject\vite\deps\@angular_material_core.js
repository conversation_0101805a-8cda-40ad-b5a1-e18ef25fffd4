import {
  Animation<PERSON>urves,
  AnimationDurations,
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY,
  MAT_NATIVE_DATE_FORMATS,
  MatLine,
  MatLineModule,
  MatNativeDateModule,
  NativeDateAdapter,
  NativeDateModule,
  VERSION,
  provideNativeDateAdapter,
  setLines
} from "./chunk-W63WCANQ.js";
import {
  _MatInternalFormField
} from "./chunk-T3FVHANY.js";
import {
  ErrorStateMatcher,
  ShowOnDirtyErrorStateMatcher,
  _ErrorStateTracker
} from "./chunk-CFIWWTE3.js";
import {
  MatRippleLoader
} from "./chunk-M4VQW47N.js";
import {
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  Mat<PERSON>eu<PERSON>C<PERSON>ckbox,
  Mat<PERSON>eu<PERSON>CheckboxModule,
  _countGroupLabelsBeforeOption,
  _getOptionScrollPosition
} from "./chunk-L22AEIXG.js";
import "./chunk-SZS4RJEH.js";
import {
  MatRippleModule
} from "./chunk-7EWSVECA.js";
import {
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatRipple,
  RippleRef,
  RippleRenderer,
  RippleState,
  defaultRippleAnimationConfig
} from "./chunk-2C3KFKAF.js";
import {
  _StructuralStylesLoader
} from "./chunk-VQTRJ4G6.js";
import "./chunk-IDNV3ES2.js";
import "./chunk-F5YF3NDX.js";
import "./chunk-2AA2HD2T.js";
import {
  MATERIAL_SANITY_CHECKS,
  MatCommonModule
} from "./chunk-WDP5JZQO.js";
import "./chunk-M3HR6BUY.js";
import "./chunk-CIGKH54X.js";
import "./chunk-52UUF34M.js";
import "./chunk-7SS675IO.js";
import "./chunk-N6X23STR.js";
import "./chunk-KRTKXBNA.js";
import "./chunk-Q34FDIAE.js";
import "./chunk-Y3P5KD7I.js";
import "./chunk-WPM5VTLQ.js";
import "./chunk-PEBH6BBU.js";
import "./chunk-4S3KYZTJ.js";
import "./chunk-V4F5PRXT.js";
export {
  AnimationCurves,
  AnimationDurations,
  DateAdapter,
  ErrorStateMatcher,
  MATERIAL_SANITY_CHECKS,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY,
  MAT_NATIVE_DATE_FORMATS,
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatCommonModule,
  MatLine,
  MatLineModule,
  MatNativeDateModule,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  MatPseudoCheckbox,
  MatPseudoCheckboxModule,
  MatRipple,
  MatRippleLoader,
  MatRippleModule,
  NativeDateAdapter,
  NativeDateModule,
  RippleRef,
  RippleRenderer,
  RippleState,
  ShowOnDirtyErrorStateMatcher,
  VERSION,
  _ErrorStateTracker,
  _MatInternalFormField,
  _StructuralStylesLoader,
  _countGroupLabelsBeforeOption,
  _getOptionScrollPosition,
  defaultRippleAnimationConfig,
  provideNativeDateAdapter,
  setLines
};
